#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MD转PDF工具使用示例
演示各种使用场景和功能
"""

import os
import sys
import time
from pathlib import Path


def print_header(title):
    """打印标题"""
    print("\n" + "=" * 50)
    print(f"🎯 {title}")
    print("=" * 50)


def print_step(step, description):
    """打印步骤"""
    print(f"\n📌 步骤 {step}: {description}")
    print("-" * 30)


def example_1_basic_conversion():
    """示例1: 基础转换"""
    print_header("示例1: 基础单文件转换")
    
    print_step(1, "检查演示文档")
    demo_file = "demo_document.md"
    
    if not os.path.exists(demo_file):
        print(f"❌ 演示文档不存在: {demo_file}")
        return False
    
    print(f"✅ 找到演示文档: {demo_file}")
    file_size = os.path.getsize(demo_file)
    print(f"📊 文件大小: {file_size:,} 字节")
    
    print_step(2, "执行转换")
    
    try:
        from md_to_pdf import MarkdownToPDFConverter
        
        converter = MarkdownToPDFConverter()
        output_file = converter.convert(demo_file)
        
        if os.path.exists(output_file):
            pdf_size = os.path.getsize(output_file)
            print(f"✅ 转换成功!")
            print(f"📄 输出文件: {output_file}")
            print(f"📊 PDF大小: {pdf_size:,} 字节")
            return True
        else:
            print("❌ 转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 转换出错: {str(e)}")
        return False


def example_2_custom_options():
    """示例2: 自定义选项转换"""
    print_header("示例2: 自定义选项转换")
    
    print_step(1, "创建测试文档")
    
    test_content = """# 自定义选项测试文档

## 页面设置测试

这是一个用于测试自定义页面设置的文档。

### 内容区域

- 页面大小: A4
- 边距: 1英寸
- 字体: 中文字体

### 代码示例

```python
def custom_settings():
    return {
        'page-size': 'A4',
        'margin': '1in',
        'encoding': 'UTF-8'
    }
```

### 表格示例

| 设置项 | 值 | 说明 |
|--------|----|----|
| 页面大小 | A4 | 标准A4纸张 |
| 边距 | 1in | 1英寸边距 |
| 编码 | UTF-8 | 支持中文 |

完成时间: 2025年1月
"""
    
    test_file = "custom_test.md"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 创建测试文档: {test_file}")
    
    print_step(2, "使用自定义选项转换")
    
    try:
        from md_to_pdf import MarkdownToPDFConverter
        
        # 自定义选项
        custom_options = {
            'page-size': 'A4',
            'margin-top': '1in',
            'margin-right': '1in',
            'margin-bottom': '1in',
            'margin-left': '1in',
            'encoding': 'UTF-8'
        }
        
        converter = MarkdownToPDFConverter()
        output_file = "custom_test_output.pdf"
        
        converter.convert(test_file, output_file, custom_options)
        
        if os.path.exists(output_file):
            print(f"✅ 自定义转换成功!")
            print(f"📄 输出文件: {output_file}")
            print(f"⚙️  使用选项: {custom_options}")
            return True
        else:
            print("❌ 转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 转换出错: {str(e)}")
        return False


def example_3_batch_conversion():
    """示例3: 批量转换"""
    print_header("示例3: 批量转换演示")
    
    print_step(1, "创建多个测试文档")
    
    # 创建多个测试文档
    test_docs = []
    for i in range(3):
        content = f"""# 批量测试文档 {i+1}

## 文档信息
- 文档编号: {i+1}
- 创建时间: 2025年1月
- 用途: 批量转换测试

## 内容示例

这是第 {i+1} 个测试文档，用于演示批量转换功能。

### 代码示例
```python
def document_{i+1}():
    print(f"这是第 {i+1} 个文档")
    return {i+1}
```

### 列表示例
- 项目 {i+1}.1
- 项目 {i+1}.2
- 项目 {i+1}.3

完成!
"""
        filename = f"batch_test_{i+1}.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        test_docs.append(filename)
        print(f"  ✅ 创建: {filename}")
    
    print_step(2, "执行批量转换")
    
    try:
        import subprocess
        
        # 执行批量转换命令
        result = subprocess.run([
            sys.executable, 'batch_md_to_pdf.py', '.',
            '--exclude', 'README_PDF_CONVERTER.md', 'demo_document.md'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 批量转换成功!")
            print("📊 转换输出:")
            print(result.stdout)
            
            # 检查生成的PDF文件
            pdf_count = 0
            for doc in test_docs:
                pdf_file = doc.replace('.md', '.pdf')
                if os.path.exists(pdf_file):
                    pdf_count += 1
                    print(f"  ✅ 生成: {pdf_file}")
            
            print(f"📈 成功率: {pdf_count}/{len(test_docs)}")
            return pdf_count == len(test_docs)
        else:
            print("❌ 批量转换失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 批量转换出错: {str(e)}")
        return False


def example_4_interactive_demo():
    """示例4: 交互式功能演示"""
    print_header("示例4: 交互式功能演示")
    
    print("🤖 交互式转换工具可以:")
    print("  - 自动列出当前目录的Markdown文件")
    print("  - 让用户选择要转换的文件")
    print("  - 自定义输出文件名")
    print("  - 支持连续转换多个文件")
    
    print("\n💡 要使用交互式模式，请运行:")
    print("   python quick_convert.py --interactive")
    print("   或者")
    print("   make interactive")
    
    return True


def cleanup_test_files():
    """清理测试文件"""
    print_header("清理测试文件")
    
    import glob
    
    patterns = [
        "custom_test.*",
        "batch_test_*.*"
    ]
    
    cleaned_count = 0
    for pattern in patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"  🗑️  删除: {file}")
                cleaned_count += 1
            except Exception as e:
                print(f"  ❌ 删除失败: {file} - {str(e)}")
    
    print(f"\n✅ 清理完成，删除了 {cleaned_count} 个文件")


def main():
    """主函数"""
    print("🚀 MD转PDF工具使用示例演示")
    print("=" * 60)
    print("本脚本将演示MD转PDF工具的各种使用场景")
    
    # 检查依赖
    try:
        from md_to_pdf import MarkdownToPDFConverter
        print("✅ MD转PDF工具已就绪")
    except ImportError:
        print("❌ 缺少依赖，请先运行: pip install -r requirements_pdf.txt")
        return False
    
    examples = [
        ("基础转换", example_1_basic_conversion),
        ("自定义选项", example_2_custom_options),
        ("批量转换", example_3_batch_conversion),
        ("交互式功能", example_4_interactive_demo)
    ]
    
    results = []
    
    for name, func in examples:
        try:
            print(f"\n⏳ 开始执行: {name}")
            result = func()
            results.append((name, result))
            
            if result:
                print(f"✅ {name} 演示完成")
            else:
                print(f"❌ {name} 演示失败")
                
            time.sleep(1)  # 短暂暂停
            
        except Exception as e:
            print(f"❌ {name} 演示出错: {str(e)}")
            results.append((name, False))
    
    # 清理测试文件
    cleanup_test_files()
    
    # 输出总结
    print_header("演示总结")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print("📊 演示结果:")
    for name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  - {name}: {status}")
    
    print(f"\n📈 成功率: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 所有演示都成功完成!")
        print("\n💡 现在您可以:")
        print("  - 使用 python md_to_pdf.py your_file.md 转换单个文件")
        print("  - 使用 python batch_md_to_pdf.py 批量转换")
        print("  - 使用 python quick_convert.py -i 交互式转换")
        print("  - 使用 make 命令快速操作")
    else:
        print("\n⚠️  部分演示失败，请检查错误信息")
    
    return success_count == total_count


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 用户取消演示")
        sys.exit(0)
