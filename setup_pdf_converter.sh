#!/bin/bash
# MD转PDF工具安装脚本

echo "🚀 开始安装MD转PDF工具..."

# 检查操作系统
OS="$(uname -s)"
case "${OS}" in
    Linux*)     MACHINE=Linux;;
    Darwin*)    MACHINE=Mac;;
    CYGWIN*)    MACHINE=Cygwin;;
    MINGW*)     MACHINE=MinGw;;
    *)          MACHINE="UNKNOWN:${OS}"
esac

echo "📱 检测到操作系统: ${MACHINE}"

# 安装wkhtmltopdf
echo "📦 安装wkhtmltopdf..."

if [ "$MACHINE" = "Mac" ]; then
    # macOS
    if command -v brew &> /dev/null; then
        echo "  使用Homebrew安装wkhtmltopdf..."
        brew install wkhtmltopdf
    else
        echo "❌ 未找到Homebrew，请先安装Homebrew或手动安装wkhtmltopdf"
        echo "   Homebrew安装: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
elif [ "$MACHINE" = "Linux" ]; then
    # Linux
    if command -v apt-get &> /dev/null; then
        echo "  使用apt-get安装wkhtmltopdf..."
        sudo apt-get update
        sudo apt-get install -y wkhtmltopdf
    elif command -v yum &> /dev/null; then
        echo "  使用yum安装wkhtmltopdf..."
        sudo yum install -y wkhtmltopdf
    elif command -v dnf &> /dev/null; then
        echo "  使用dnf安装wkhtmltopdf..."
        sudo dnf install -y wkhtmltopdf
    else
        echo "❌ 未找到包管理器，请手动安装wkhtmltopdf"
        exit 1
    fi
else
    echo "❌ 不支持的操作系统: ${MACHINE}"
    echo "   请手动安装wkhtmltopdf: https://wkhtmltopdf.org/downloads.html"
    exit 1
fi

# 检查wkhtmltopdf是否安装成功
if command -v wkhtmltopdf &> /dev/null; then
    echo "✅ wkhtmltopdf安装成功"
    wkhtmltopdf --version
else
    echo "❌ wkhtmltopdf安装失败"
    exit 1
fi

# 安装Python依赖
echo "🐍 安装Python依赖..."

if [ -f "requirements_pdf.txt" ]; then
    pip install -r requirements_pdf.txt
    if [ $? -eq 0 ]; then
        echo "✅ Python依赖安装成功"
    else
        echo "❌ Python依赖安装失败"
        exit 1
    fi
else
    echo "❌ 未找到requirements_pdf.txt文件"
    exit 1
fi

# 设置执行权限
echo "🔧 设置执行权限..."
chmod +x md_to_pdf.py
chmod +x batch_md_to_pdf.py
chmod +x test_converter.py

# 运行测试
echo "🧪 运行测试..."
python test_converter.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 MD转PDF工具安装完成!"
    echo ""
    echo "📖 使用方法:"
    echo "  单文件转换: python md_to_pdf.py your_file.md"
    echo "  批量转换:   python batch_md_to_pdf.py"
    echo "  查看帮助:   python md_to_pdf.py --help"
    echo ""
    echo "📚 详细说明请查看: README_PDF_CONVERTER.md"
else
    echo "❌ 测试失败，请检查安装"
    exit 1
fi
