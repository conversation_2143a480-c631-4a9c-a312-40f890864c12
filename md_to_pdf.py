#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown to PDF Converter
将Markdown文件转换为PDF格式的工具

支持功能：
- 中文字体支持
- 代码高亮
- 表格格式化
- 图片嵌入
- 自定义样式
"""

import os
import sys
import argparse
import markdown
import pdfkit
from pathlib import Path
import tempfile
import shutil
from typing import Optional, List


class MarkdownToPDFConverter:
    """Markdown转PDF转换器"""
    
    def __init__(self):
        self.wkhtmltopdf_path = self._find_wkhtmltopdf()
        self.css_template = self._get_default_css()
        
    def _find_wkhtmltopdf(self) -> Optional[str]:
        """查找wkhtmltopdf可执行文件路径"""
        possible_paths = [
            '/usr/local/bin/wkhtmltopdf',
            '/usr/bin/wkhtmltopdf',
            '/opt/homebrew/bin/wkhtmltopdf',
            shutil.which('wkhtmltopdf')
        ]
        
        for path in possible_paths:
            if path and os.path.exists(path):
                return path
        return None
    
    def _get_default_css(self) -> str:
        """获取默认CSS样式"""
        return """
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        
        body {
            font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-size: 14px;
        }
        
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }
        
        h1 {
            font-size: 28px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        h2 {
            font-size: 24px;
            border-bottom: 2px solid #e74c3c;
            padding-bottom: 8px;
        }
        
        h3 {
            font-size: 20px;
            color: #e67e22;
        }
        
        h4 {
            font-size: 18px;
            color: #9b59b6;
        }
        
        p {
            margin-bottom: 16px;
            text-align: justify;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            color: #e74c3c;
        }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 16px;
            overflow-x: auto;
            margin: 16px 0;
        }
        
        pre code {
            background-color: transparent;
            padding: 0;
            color: #333;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
            font-size: 13px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: 600;
            color: #2c3e50;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            margin: 16px 0;
            padding: 0 16px;
            color: #666;
            background-color: #f8f9fa;
        }
        
        ul, ol {
            margin: 16px 0;
            padding-left: 24px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        img {
            max-width: 100%;
            height: auto;
            margin: 16px 0;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        hr {
            border: none;
            border-top: 2px solid #ecf0f1;
            margin: 32px 0;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                font-size: 12px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            h2 {
                font-size: 20px;
            }
            
            h3 {
                font-size: 18px;
            }
        }
        </style>
        """
    
    def convert_md_to_html(self, md_file: str) -> str:
        """将Markdown转换为HTML"""
        try:
            with open(md_file, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            # 配置markdown扩展
            extensions = [
                'markdown.extensions.tables',
                'markdown.extensions.fenced_code',
                'markdown.extensions.codehilite',
                'markdown.extensions.toc',
                'markdown.extensions.meta'
            ]
            
            # 转换为HTML
            md = markdown.Markdown(extensions=extensions)
            html_content = md.convert(md_content)
            
            # 构建完整的HTML文档
            full_html = f"""
            <!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Markdown to PDF</title>
                {self.css_template}
            </head>
            <body>
                {html_content}
            </body>
            </html>
            """
            
            return full_html
            
        except Exception as e:
            raise Exception(f"转换Markdown到HTML失败: {str(e)}")
    
    def convert_html_to_pdf(self, html_content: str, output_file: str, options: dict = None) -> bool:
        """将HTML转换为PDF"""
        if not self.wkhtmltopdf_path:
            raise Exception("未找到wkhtmltopdf，请先安装: brew install wkhtmltopdf")
        
        # 默认选项
        default_options = {
            'page-size': 'A4',
            'margin-top': '0.75in',
            'margin-right': '0.75in',
            'margin-bottom': '0.75in',
            'margin-left': '0.75in',
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None,
            'print-media-type': None
        }
        
        if options:
            default_options.update(options)
        
        try:
            # 配置pdfkit
            config = pdfkit.configuration(wkhtmltopdf=self.wkhtmltopdf_path)
            
            # 转换为PDF
            pdfkit.from_string(html_content, output_file, options=default_options, configuration=config)
            return True
            
        except Exception as e:
            raise Exception(f"转换HTML到PDF失败: {str(e)}")
    
    def convert(self, input_file: str, output_file: str = None, options: dict = None) -> str:
        """转换Markdown文件为PDF"""
        # 检查输入文件
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        # 确定输出文件名
        if not output_file:
            input_path = Path(input_file)
            output_file = str(input_path.with_suffix('.pdf'))
        
        print(f"开始转换: {input_file} -> {output_file}")
        
        try:
            # 转换MD到HTML
            html_content = self.convert_md_to_html(input_file)
            
            # 转换HTML到PDF
            self.convert_html_to_pdf(html_content, output_file, options)
            
            print(f"转换完成: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"转换失败: {str(e)}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Markdown to PDF Converter')
    parser.add_argument('input', help='输入的Markdown文件路径')
    parser.add_argument('-o', '--output', help='输出的PDF文件路径')
    parser.add_argument('--page-size', default='A4', help='页面大小 (默认: A4)')
    parser.add_argument('--margin', default='0.75in', help='页边距 (默认: 0.75in)')
    
    args = parser.parse_args()
    
    # 自定义选项
    options = {
        'page-size': args.page_size,
        'margin-top': args.margin,
        'margin-right': args.margin,
        'margin-bottom': args.margin,
        'margin-left': args.margin,
    }
    
    try:
        converter = MarkdownToPDFConverter()
        output_file = converter.convert(args.input, args.output, options)
        print(f"✅ 转换成功! PDF文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
