#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量Markdown转PDF工具
支持批量转换目录下的所有Markdown文件
"""

import os
import sys
import argparse
import glob
from pathlib import Path
from md_to_pdf import MarkdownToPDFConverter


def find_markdown_files(directory: str, recursive: bool = False) -> list:
    """查找目录下的Markdown文件"""
    patterns = ['*.md', '*.markdown', '*.mdown', '*.mkd']
    files = []
    
    for pattern in patterns:
        if recursive:
            search_pattern = os.path.join(directory, '**', pattern)
            files.extend(glob.glob(search_pattern, recursive=True))
        else:
            search_pattern = os.path.join(directory, pattern)
            files.extend(glob.glob(search_pattern))
    
    return sorted(list(set(files)))


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量Markdown转PDF工具')
    parser.add_argument('directory', nargs='?', default='.', help='要转换的目录路径 (默认: 当前目录)')
    parser.add_argument('-r', '--recursive', action='store_true', help='递归搜索子目录')
    parser.add_argument('-o', '--output-dir', help='输出目录 (默认: 与源文件同目录)')
    parser.add_argument('--page-size', default='A4', help='页面大小 (默认: A4)')
    parser.add_argument('--margin', default='0.75in', help='页边距 (默认: 0.75in)')
    parser.add_argument('--exclude', nargs='*', default=[], help='排除的文件名模式')
    
    args = parser.parse_args()
    
    # 检查目录
    if not os.path.exists(args.directory):
        print(f"❌ 目录不存在: {args.directory}")
        sys.exit(1)
    
    # 查找Markdown文件
    print(f"🔍 在目录 '{args.directory}' 中查找Markdown文件...")
    md_files = find_markdown_files(args.directory, args.recursive)
    
    # 过滤排除的文件
    if args.exclude:
        filtered_files = []
        for file in md_files:
            filename = os.path.basename(file)
            should_exclude = False
            for pattern in args.exclude:
                if pattern in filename:
                    should_exclude = True
                    break
            if not should_exclude:
                filtered_files.append(file)
        md_files = filtered_files
    
    if not md_files:
        print("❌ 未找到Markdown文件")
        sys.exit(1)
    
    print(f"📄 找到 {len(md_files)} 个Markdown文件:")
    for file in md_files:
        print(f"  - {file}")
    
    # 转换选项
    options = {
        'page-size': args.page_size,
        'margin-top': args.margin,
        'margin-right': args.margin,
        'margin-bottom': args.margin,
        'margin-left': args.margin,
    }
    
    # 初始化转换器
    try:
        converter = MarkdownToPDFConverter()
    except Exception as e:
        print(f"❌ 初始化转换器失败: {str(e)}")
        sys.exit(1)
    
    # 批量转换
    success_count = 0
    failed_files = []
    
    print(f"\n🚀 开始批量转换...")
    
    for i, md_file in enumerate(md_files, 1):
        try:
            # 确定输出文件路径
            if args.output_dir:
                os.makedirs(args.output_dir, exist_ok=True)
                filename = Path(md_file).stem + '.pdf'
                output_file = os.path.join(args.output_dir, filename)
            else:
                output_file = str(Path(md_file).with_suffix('.pdf'))
            
            print(f"[{i}/{len(md_files)}] 转换: {os.path.basename(md_file)}")
            
            # 执行转换
            converter.convert(md_file, output_file, options)
            success_count += 1
            print(f"  ✅ 成功: {output_file}")
            
        except Exception as e:
            failed_files.append((md_file, str(e)))
            print(f"  ❌ 失败: {str(e)}")
    
    # 输出结果统计
    print(f"\n📊 转换完成!")
    print(f"  ✅ 成功: {success_count} 个文件")
    print(f"  ❌ 失败: {len(failed_files)} 个文件")
    
    if failed_files:
        print(f"\n❌ 失败的文件:")
        for file, error in failed_files:
            print(f"  - {file}: {error}")
    
    print(f"\n🎉 批量转换完成!")


if __name__ == "__main__":
    main()
