#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试
"""

import sys
import os
import unittest

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.note_model import note_db, NoteInfo
from src.staff_renderer import StaffRenderer
from src.audio_player import AudioPlayer

class TestNoteModel(unittest.TestCase):
    """测试音符数据模型"""
    
    def test_note_database_initialization(self):
        """测试音符数据库初始化"""
        # 检查是否有音符数据
        self.assertGreater(len(note_db.notes), 0)
        
        # 检查白键数量（应该有17个白键：A3-C6）
        white_keys = note_db.get_white_keys()
        self.assertEqual(len(white_keys), 17)
        
        # 检查黑键数量
        black_keys = note_db.get_black_keys()
        self.assertEqual(len(black_keys), 11)
    
    def test_specific_notes(self):
        """测试特定音符"""
        # 测试中央C
        c4 = note_db.get_note('C4')
        self.assertIsNotNone(c4)
        self.assertEqual(c4.note_name, 'C')
        self.assertEqual(c4.solfege, 'do')
        self.assertEqual(c4.octave, 4)
        self.assertAlmostEqual(c4.frequency, 261.63, places=1)
        self.assertFalse(c4.is_black_key)
        
        # 测试A4 (标准音高)
        a4 = note_db.get_note('A4')
        self.assertIsNotNone(a4)
        self.assertEqual(a4.frequency, 440.0)
        
        # 测试黑键
        cs4 = note_db.get_note('C#4')
        self.assertIsNotNone(cs4)
        self.assertTrue(cs4.is_black_key)
        self.assertEqual(cs4.solfege, '')
    
    def test_random_note(self):
        """测试随机音符获取"""
        note = note_db.get_random_white_key()
        self.assertIsNotNone(note)
        self.assertFalse(note.is_black_key)

class TestAudioPlayer(unittest.TestCase):
    """测试音频播放器"""
    
    def setUp(self):
        """设置测试"""
        self.audio_player = AudioPlayer(duration=0.1)  # 短时间测试
    
    def test_audio_initialization(self):
        """测试音频初始化"""
        # 注意：在没有音频设备的环境中可能会失败
        # self.assertTrue(self.audio_player.is_initialized)
        pass
    
    def test_tone_generation(self):
        """测试音调生成"""
        if self.audio_player.is_initialized:
            sound = self.audio_player.generate_tone(440.0, 0.1)
            self.assertIsNotNone(sound)
    
    def tearDown(self):
        """清理测试"""
        self.audio_player.cleanup()

def run_basic_tests():
    """运行基本测试"""
    print("开始运行基本功能测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试
    suite.addTest(unittest.makeSuite(TestNoteModel))
    suite.addTest(unittest.makeSuite(TestAudioPlayer))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_basic_tests()
    if success:
        print("\n✅ 所有基本测试通过！")
    else:
        print("\n❌ 部分测试失败！")
    
    sys.exit(0 if success else 1)
