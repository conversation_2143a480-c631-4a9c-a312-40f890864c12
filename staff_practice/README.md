# 五线谱视奏练习程序

一个面向初学者的五线谱视奏练习程序，帮助用户学习识别音符位置和对应的音名、唱名，并提供交互式钢琴键盘功能。

## 功能特点

### 1. 视奏练习模式
- 随机显示五线谱上的音符（A3-C6范围）
- 自动播放对应音高
- 显示/隐藏音名和唱名答案
- 支持重播功能

### 2. 钢琴键盘模式
- 虚拟钢琴键盘（A3-C6，包含白键和黑键）
- 点击钢琴键播放音符并在五线谱上显示
- 支持连续点击显示多个音符

### 3. 音频功能
- 基于频率的纯音调生成
- 实时音频播放
- 音符与声音同步

### 4. 用户界面
- 直观的五线谱显示
- 高音谱号和标准记谱法
- 音名唱名高亮显示
- 模式切换功能

## 安装和运行

### 系统要求
- Python 3.7+
- 支持音频输出的系统

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python main.py
```

## 使用说明

### 视奏练习模式
1. 选择"视奏练习"模式
2. 点击"下一题"按钮或按空格键显示随机音符
3. 观察音符位置，思考对应的音名和唱名
4. 点击"重播"按钮或按R键重新听音
5. 点击"显示答案"按钮或按A键查看正确答案

### 钢琴键盘模式
1. 选择"钢琴键盘"模式
2. 点击虚拟钢琴键，听到对应音高
3. 同时在五线谱上看到对应音符出现
4. 可连续点击多个键，在五线谱上显示音符序列
5. 点击"清除五线谱"按钮或按C键重新开始

### 快捷键
- 空格键：下一题（视奏模式）
- R键：重播当前音符
- A键：显示/隐藏答案
- C键：清除五线谱

## 音符范围

程序支持从A3到C6的音符范围：

| 位置 | 音高 | 音名 | 唱名 |
|------|------|------|------|
| 下加二线 | A3 | A | la |
| 下加一线 | C4 | C | do |
| 第一线 | E4 | E | mi |
| 第一间 | F4 | F | fa |
| 第二线 | G4 | G | sol |
| 第二间 | A4 | A | la |
| 第三线 | B4 | B | si |
| 第三间 | C5 | C | do |
| 第四线 | D5 | D | re |
| 第四间 | E5 | E | mi |
| 第五线 | F5 | F | fa |
| 上加一线 | A5 | A | la |
| 上加二线 | C6 | C | do |

## 项目结构

```
staff_practice/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖列表
├── README.md              # 说明文档
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── note_model.py      # 音符数据模型
│   ├── staff_renderer.py  # 五线谱绘制模块
│   ├── piano_keyboard.py  # 钢琴键盘模块
│   ├── audio_player.py    # 音频播放模块
│   └── staff_practice_app.py # 主应用程序类
├── tests/                 # 测试目录
│   └── test_basic.py      # 基本功能测试
└── assets/                # 资源目录（预留）
```

## 技术实现

- **GUI框架**: Tkinter
- **音频处理**: pygame.mixer + numpy
- **音符生成**: 基于频率的正弦波合成
- **图形绘制**: Tkinter Canvas

## 运行测试

```bash
python tests/test_basic.py
```

## 故障排除

### 音频问题
如果遇到音频播放问题：
1. 确保系统音频设备正常工作
2. 检查pygame是否正确安装
3. 尝试重新安装音频依赖

### 显示问题
如果界面显示异常：
1. 确保使用支持Unicode的字体
2. 检查系统是否支持Tkinter
3. 尝试调整窗口大小

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交问题报告和功能建议！
