#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五线谱视奏练习程序演示脚本
展示程序的主要功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.note_model import note_db
from src.audio_player import AudioPlayer

def demo_note_database():
    """演示音符数据库功能"""
    print("=== 音符数据库演示 ===")
    
    # 显示所有白键
    white_keys = note_db.get_white_keys()
    print(f"白键数量: {len(white_keys)}")
    print("白键列表:")
    for note in white_keys:
        print(f"  {note.pitch}: {note.note_name} ({note.solfege}) - {note.frequency:.2f}Hz")
    
    print()
    
    # 显示所有黑键
    black_keys = note_db.get_black_keys()
    print(f"黑键数量: {len(black_keys)}")
    print("黑键列表:")
    for note in black_keys:
        print(f"  {note.pitch}: {note.note_name} - {note.frequency:.2f}Hz")
    
    print()
    
    # 测试特定音符
    print("特定音符测试:")
    test_notes = ['C4', 'A4', 'F#4', 'C6']
    for pitch in test_notes:
        note = note_db.get_note(pitch)
        if note:
            print(f"  {pitch}: {note.note_name} ({note.solfege}) - 五线谱位置: {note.position}")
    
    print()

def demo_audio_player():
    """演示音频播放功能"""
    print("=== 音频播放演示 ===")
    
    audio_player = AudioPlayer(duration=0.5)
    
    if not audio_player.is_initialized:
        print("❌ 音频系统初始化失败")
        return
    
    print("✅ 音频系统初始化成功")
    
    # 播放一些音符
    test_notes = ['C4', 'E4', 'G4', 'C5']
    print(f"播放测试音符: {', '.join(test_notes)}")
    
    for pitch in test_notes:
        note = note_db.get_note(pitch)
        if note:
            print(f"播放 {pitch} ({note.frequency:.2f}Hz)...")
            audio_player.play_note(note, duration=0.8)
            import time
            time.sleep(1.0)  # 等待播放完成
    
    audio_player.cleanup()
    print("音频演示完成")
    print()

def demo_random_notes():
    """演示随机音符功能"""
    print("=== 随机音符演示 ===")
    
    print("生成5个随机白键音符:")
    for i in range(5):
        note = note_db.get_random_white_key()
        print(f"  {i+1}. {note.pitch}: {note.note_name} ({note.solfege}) - {note.frequency:.2f}Hz")
    
    print()

def main():
    """主演示函数"""
    print("🎵 五线谱视奏练习程序功能演示 🎵")
    print("=" * 50)
    print()
    
    try:
        # 演示音符数据库
        demo_note_database()
        
        # 演示随机音符
        demo_random_notes()
        
        # 演示音频播放（可选）
        print("是否要演示音频播放功能？(y/n): ", end="")
        response = input().strip().lower()
        if response in ['y', 'yes', '是']:
            demo_audio_player()
        else:
            print("跳过音频演示")
            print()
        
        print("✅ 所有功能演示完成！")
        print()
        print("要运行完整的图形界面程序，请执行:")
        print("  python main.py")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
