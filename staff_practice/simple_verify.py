#!/usr/bin/env python3

# 直接导入音符数据模型，不依赖pygame
import sys
import os
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class NoteInfo:
    """音符信息数据类"""
    pitch: str           # 音高标记，如 'C4'
    position: str        # 在五线谱上的位置，如 'ledger_-1'
    frequency: float     # 频率(Hz)
    note_name: str       # 音名，如 'C'
    solfege: str         # 唱名，如 'do'
    is_black_key: bool   # 是否为黑键
    octave: int          # 八度，如 4
    staff_y: int         # 在五线谱上的Y坐标位置（相对于第三线）

# 创建测试音符
test_notes = [
    ('C4', 'ledger_-1', -6),   # 下加一线
    ('D4', 'space_below_1', -5), # 下加一线和第一线之间
    ('E4', 'line_1', -4),      # 第一线
    ('F4', 'space_1', -3),     # 第一间
    ('G4', 'line_2', -2),      # 第二线
    ('A4', 'space_2', -1),     # 第二间
    ('B4', 'line_3', 0),       # 第三线
    ('C5', 'space_3', 1),      # 第三间
]

print("=== 音符位置验证 ===")
print("音符 | 位置描述           | staff_y | 类型")
print("-" * 45)

for pitch, position, expected_y in test_notes:
    note_type = "线上" if expected_y % 2 == 0 else "间中"
    print(f"{pitch:4s} | {position:15s} | {expected_y:7d} | {note_type}")

print("\n验证说明:")
print("- staff_y为偶数的音符应该在线上")
print("- staff_y为奇数的音符应该在间中")
print("- 第三线(B4)的staff_y=0作为基准")
print("- 每个单位代表半个间距")

print("\n关键位置:")
print("C4 (下加一线): staff_y = -6")
print("D4 (间中):     staff_y = -5") 
print("E4 (第一线):   staff_y = -4")
print("B4 (第三线):   staff_y = 0")
