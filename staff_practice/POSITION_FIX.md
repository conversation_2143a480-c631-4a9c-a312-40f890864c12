# 音符位置修复说明

## 修复的问题

根据用户反馈，修复了以下两个关键问题：

### 1. 加线间距问题 ✅
**问题**: 上下加线的间距与五线谱不一致
**解决方案**: 
- 重新设计了坐标系统，确保加线间距与五线谱完全一致
- 使用统一的 `line_spacing // 2` 作为半间距单位
- 加线位置精确计算，与五线谱保持相同间距

### 2. 音符位置精度问题 ✅
**问题**: 音符位置不够精确，特别是间位音符没有在正中间
**解决方案**:
- 重新定义了 `staff_y` 坐标系统
- 以第三线为基准点 (staff_y = 0)
- 每个单位代表一个半间距 (line_spacing / 2)
- 确保线上音符和间中音符位置精确

## 新的坐标系统

### staff_y 值定义
```
staff_y = 8:  上加二线 (C6)
staff_y = 7:  上加一线和上加二线之间 (B5)
staff_y = 6:  上加一线 (A5)
staff_y = 5:  第五线上方 (G5)
staff_y = 4:  第五线 (F5)
staff_y = 3:  第四间 (E5)
staff_y = 2:  第四线 (D5)
staff_y = 1:  第三间 (C5)
staff_y = 0:  第三线 (B4) ← 基准线
staff_y = -1: 第二间 (A4)
staff_y = -2: 第二线 (G4)
staff_y = -3: 第一间 (F4)
staff_y = -4: 第一线 (E4)
staff_y = -5: 下加一线和第一线之间 (D4)
staff_y = -6: 下加一线 (C4)
staff_y = -7: 下加一线和下加二线之间 (B3)
staff_y = -8: 下加二线 (A3)
```

### 位置规律
- **线上音符**: staff_y 为偶数 (-8, -6, -4, -2, 0, 2, 4, 6, 8)
- **间中音符**: staff_y 为奇数 (-7, -5, -3, -1, 1, 3, 5, 7)

## 修改的文件

### 1. `src/note_model.py`
- 更新了 `position_y_map` 字典
- 重新定义了所有音符的 `staff_y` 值
- 确保线上音符为偶数，间中音符为奇数

### 2. `src/staff_renderer.py`
- 修改了 `_get_note_y_position()` 方法
- 更新了 `_draw_ledger_lines()` 方法
- 修正了 `_draw_note_stem()` 中的符干方向判断

## 验证结果

### ✅ 关键音符位置验证
- C4 (下加一线): staff_y = -6 ✅
- D4 (间中): staff_y = -5 ✅
- E4 (第一线): staff_y = -4 ✅
- F4 (第一间): staff_y = -3 ✅
- G4 (第二线): staff_y = -2 ✅
- A4 (第二间): staff_y = -1 ✅
- B4 (第三线): staff_y = 0 ✅
- C5 (第三间): staff_y = 1 ✅

### ✅ 加线绘制验证
- 下加一线和下加二线间距正确 ✅
- 上加一线和上加二线间距正确 ✅
- 所有加线与五线谱间距一致 ✅

### ✅ 程序运行验证
- 基本测试全部通过 ✅
- 主程序正常启动 ✅
- 图形界面正常显示 ✅

## 使用说明

修复后的程序现在可以：
1. 精确显示音符在五线谱上的正确位置
2. 正确绘制加线，间距与五线谱一致
3. 确保间位音符（如D4）显示在线与线的正中间
4. 确保线上音符（如C4）精确显示在线上

用户可以通过运行 `python main.py` 来体验修复后的程序。
