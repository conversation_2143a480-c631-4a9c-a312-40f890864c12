#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音符位置是否正确
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.note_model import note_db

def test_note_positions():
    """测试关键音符的位置"""
    print("=== 音符位置测试 ===")
    
    # 测试关键音符位置
    test_notes = [
        ('A3', '下加二线'),
        ('B3', '下加一线和下加二线之间'),
        ('C4', '下加一线'),
        ('D4', '下加一线和第一线之间'),
        ('E4', '第一线'),
        ('F4', '第一间'),
        ('G4', '第二线'),
        ('A4', '第二间'),
        ('B4', '第三线'),
        ('C5', '第三间'),
        ('D5', '第四线'),
        ('E5', '第四间'),
        ('F5', '第五线'),
        ('G5', '第五线上方'),
        ('A5', '上加一线'),
        ('B5', '上加一线和上加二线之间'),
        ('C6', '上加二线'),
    ]
    
    print("音符位置映射:")
    for pitch, expected_pos in test_notes:
        note = note_db.get_note(pitch)
        if note:
            print(f"{pitch:4s}: staff_y={note.staff_y:2d}, 位置={note.position:15s}, 预期={expected_pos}")
        else:
            print(f"{pitch:4s}: 未找到")
    
    print()
    
    # 验证线上音符（应该是偶数staff_y）
    print("线上音符验证:")
    line_notes = ['A3', 'C4', 'E4', 'G4', 'B4', 'D5', 'F5', 'A5', 'C6']
    for pitch in line_notes:
        note = note_db.get_note(pitch)
        if note:
            is_on_line = (note.staff_y % 2 == 0)  # 线上音符的staff_y应该是偶数
            print(f"{pitch}: staff_y={note.staff_y}, 在线上={is_on_line}")
    
    print()
    
    # 验证间中音符（应该是奇数staff_y）
    print("间中音符验证:")
    space_notes = ['B3', 'D4', 'F4', 'A4', 'C5', 'E5', 'G5', 'B5']
    for pitch in space_notes:
        note = note_db.get_note(pitch)
        if note:
            is_in_space = (note.staff_y % 2 != 0)  # 间中音符的staff_y应该是奇数
            print(f"{pitch}: staff_y={note.staff_y}, 在间中={is_in_space}")

if __name__ == "__main__":
    test_note_positions()
