#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五线谱绘制模块
负责绘制五线谱、高音谱号、音符等
"""

import tkinter as tk
from tkinter import Canvas
from typing import List, Tuple, Optional
from .note_model import NoteInfo

class StaffRenderer:
    """五线谱渲染器"""
    
    def __init__(self, canvas: Canvas, width: int = 600, height: int = 200):
        """
        初始化五线谱渲染器
        
        Args:
            canvas: Tkinter Canvas对象
            width: 五线谱宽度
            height: 五线谱高度
        """
        self.canvas = canvas
        self.width = width
        self.height = height
        
        # 五线谱参数
        self.staff_left = 80    # 五线谱左边距
        self.staff_right = width - 50  # 五线谱右边距
        self.staff_top = 50     # 五线谱顶部边距
        self.line_spacing = 15  # 线间距
        
        # 第三线的Y坐标（基准线）
        self.middle_line_y = self.staff_top + 2 * self.line_spacing
        
        # 音符显示列表
        self.displayed_notes: List[Tuple[NoteInfo, int]] = []  # (音符信息, x坐标)
        
        self._draw_staff()
    
    def _draw_staff(self):
        """绘制基本五线谱结构"""
        # 清空画布
        self.canvas.delete("all")
        
        # 绘制五条线
        for i in range(5):
            y = self.staff_top + i * self.line_spacing
            self.canvas.create_line(
                self.staff_left, y, self.staff_right, y,
                fill="black", width=1, tags="staff_line"
            )
        
        # 绘制高音谱号（简化版本）
        self._draw_treble_clef()
        
        # 重新绘制所有音符
        self._redraw_notes()
    
    def _draw_treble_clef(self):
        """绘制高音谱号（简化版本）"""
        clef_x = self.staff_left + 10
        clef_y = self.middle_line_y
        
        # 简化的高音谱号，使用文本符号
        self.canvas.create_text(
            clef_x, clef_y, text="𝄞", font=("Arial", 40),
            fill="black", tags="treble_clef"
        )
    
    def _get_note_y_position(self, note: NoteInfo) -> int:
        """
        根据音符信息计算Y坐标位置

        Args:
            note: 音符信息

        Returns:
            Y坐标位置
        """
        # 以第三线为基准（staff_y = 0）
        # 每个单位代表一个半间距（line_spacing / 2）
        half_spacing = self.line_spacing // 2
        return self.middle_line_y - note.staff_y * half_spacing
    
    def _draw_ledger_lines(self, x: int, note: NoteInfo):
        """
        绘制加线

        Args:
            x: 音符的X坐标
            note: 音符信息
        """
        ledger_length = 20
        half_spacing = self.line_spacing // 2

        # 下加线（staff_y < -4 表示在第一线以下）
        if note.staff_y <= -6:  # 下加一线及以下
            # 绘制下加一线（staff_y = -6）
            if note.staff_y <= -6:
                y = self.middle_line_y - (-6) * half_spacing
                self.canvas.create_line(
                    x - ledger_length // 2, y, x + ledger_length // 2, y,
                    fill="black", width=1, tags="ledger_line"
                )

            # 绘制下加二线（staff_y = -8）
            if note.staff_y <= -8:
                y = self.middle_line_y - (-8) * half_spacing
                self.canvas.create_line(
                    x - ledger_length // 2, y, x + ledger_length // 2, y,
                    fill="black", width=1, tags="ledger_line"
                )

        # 上加线（staff_y > 4 表示在第五线以上）
        if note.staff_y >= 6:  # 上加一线及以上
            # 绘制上加一线（staff_y = 6）
            if note.staff_y >= 6:
                y = self.middle_line_y - 6 * half_spacing
                self.canvas.create_line(
                    x - ledger_length // 2, y, x + ledger_length // 2, y,
                    fill="black", width=1, tags="ledger_line"
                )

            # 绘制上加二线（staff_y = 8）
            if note.staff_y >= 8:
                y = self.middle_line_y - 8 * half_spacing
                self.canvas.create_line(
                    x - ledger_length // 2, y, x + ledger_length // 2, y,
                    fill="black", width=1, tags="ledger_line"
                )
    
    def _draw_note_head(self, x: int, y: int, note: NoteInfo):
        """
        绘制音符符头
        
        Args:
            x: X坐标
            y: Y坐标
            note: 音符信息
        """
        # 四分音符符头（实心椭圆）
        head_width = 8
        head_height = 6
        
        self.canvas.create_oval(
            x - head_width // 2, y - head_height // 2,
            x + head_width // 2, y + head_height // 2,
            fill="black", outline="black", tags="note_head"
        )
    
    def _draw_note_stem(self, x: int, y: int, note: NoteInfo):
        """
        绘制音符符干

        Args:
            x: 符头X坐标
            y: 符头Y坐标
            note: 音符信息
        """
        stem_length = 25

        # 符干方向规则：第三线以下向上，第三线以上向下
        # 第三线的staff_y = 0
        if note.staff_y <= 0:  # 第三线及以下，符干向上
            stem_x = x + 4  # 符干在符头右侧
            stem_start_y = y
            stem_end_y = y - stem_length
        else:  # 第三线以上，符干向下
            stem_x = x - 4  # 符干在符头左侧
            stem_start_y = y
            stem_end_y = y + stem_length

        self.canvas.create_line(
            stem_x, stem_start_y, stem_x, stem_end_y,
            fill="black", width=2, tags="note_stem"
        )
    
    def add_note(self, note: NoteInfo, x: Optional[int] = None) -> int:
        """
        在五线谱上添加音符
        
        Args:
            note: 音符信息
            x: 指定的X坐标，如果为None则自动计算
            
        Returns:
            音符的X坐标
        """
        if x is None:
            # 自动计算X坐标
            if self.displayed_notes:
                last_x = self.displayed_notes[-1][1]
                x = last_x + 60
            else:
                x = self.staff_left + 100
        
        # 确保X坐标在有效范围内
        x = max(self.staff_left + 60, min(x, self.staff_right - 30))
        
        # 计算Y坐标
        y = self._get_note_y_position(note)
        
        # 绘制加线（如果需要）
        self._draw_ledger_lines(x, note)
        
        # 绘制音符
        self._draw_note_head(x, y, note)
        self._draw_note_stem(x, y, note)
        
        # 添加到显示列表
        self.displayed_notes.append((note, x))
        
        return x
    
    def clear_notes(self):
        """清除所有音符"""
        self.canvas.delete("note_head")
        self.canvas.delete("note_stem")
        self.canvas.delete("ledger_line")
        self.displayed_notes.clear()
    
    def _redraw_notes(self):
        """重新绘制所有音符"""
        notes_to_redraw = self.displayed_notes.copy()
        self.displayed_notes.clear()
        
        for note, x in notes_to_redraw:
            self.add_note(note, x)
    
    def get_displayed_notes(self) -> List[NoteInfo]:
        """获取当前显示的所有音符"""
        return [note for note, _ in self.displayed_notes]
    
    def resize(self, width: int, height: int):
        """调整五线谱大小"""
        self.width = width
        self.height = height
        self.staff_right = width - 50
        self._draw_staff()
