#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频播放模块
负责生成和播放音符对应的声音
"""

import pygame
import numpy as np
import threading
import time
from typing import Optional
from .note_model import NoteInfo

class AudioPlayer:
    """音频播放器类"""
    
    def __init__(self, sample_rate: int = 44100, duration: float = 1.0):
        """
        初始化音频播放器
        
        Args:
            sample_rate: 采样率
            duration: 音符持续时间（秒）
        """
        self.sample_rate = sample_rate
        self.duration = duration
        self.is_initialized = False
        
        try:
            # 初始化pygame mixer
            pygame.mixer.pre_init(
                frequency=sample_rate,
                size=-16,  # 16位有符号
                channels=2,  # 立体声
                buffer=512
            )
            pygame.mixer.init()
            self.is_initialized = True
        except Exception as e:
            print(f"音频初始化失败: {e}")
            self.is_initialized = False
    
    def generate_tone(self, frequency: float, duration: float = None) -> Optional[pygame.mixer.Sound]:
        """
        生成指定频率的纯音调
        
        Args:
            frequency: 频率（Hz）
            duration: 持续时间（秒），如果为None则使用默认值
            
        Returns:
            pygame.mixer.Sound对象，如果生成失败则返回None
        """
        if not self.is_initialized:
            return None
        
        if duration is None:
            duration = self.duration
        
        try:
            # 生成时间数组
            t = np.linspace(0, duration, int(self.sample_rate * duration), False)
            
            # 生成正弦波
            wave = np.sin(2 * np.pi * frequency * t)
            
            # 添加包络以避免爆音
            envelope = self._create_envelope(len(wave))
            wave = wave * envelope
            
            # 转换为16位整数
            wave = (wave * 32767).astype(np.int16)
            
            # 创建立体声
            stereo_wave = np.zeros((len(wave), 2), dtype=np.int16)
            stereo_wave[:, 0] = wave  # 左声道
            stereo_wave[:, 1] = wave  # 右声道
            
            # 创建Sound对象
            sound = pygame.mixer.Sound(stereo_wave)
            return sound
            
        except Exception as e:
            print(f"生成音调失败: {e}")
            return None
    
    def _create_envelope(self, length: int) -> np.ndarray:
        """
        创建音频包络以避免爆音
        
        Args:
            length: 音频长度
            
        Returns:
            包络数组
        """
        envelope = np.ones(length)
        
        # 淡入时间（10ms）
        fade_in_samples = int(0.01 * self.sample_rate)
        if fade_in_samples > 0:
            envelope[:fade_in_samples] = np.linspace(0, 1, fade_in_samples)
        
        # 淡出时间（50ms）
        fade_out_samples = int(0.05 * self.sample_rate)
        if fade_out_samples > 0:
            envelope[-fade_out_samples:] = np.linspace(1, 0, fade_out_samples)
        
        return envelope
    
    def play_note(self, note: NoteInfo, duration: float = None):
        """
        播放指定音符
        
        Args:
            note: 音符信息
            duration: 持续时间（秒）
        """
        if not self.is_initialized:
            print("音频系统未初始化")
            return
        
        # 在新线程中播放音频，避免阻塞UI
        thread = threading.Thread(
            target=self._play_note_thread,
            args=(note, duration),
            daemon=True
        )
        thread.start()
    
    def _play_note_thread(self, note: NoteInfo, duration: float = None):
        """
        在线程中播放音符
        
        Args:
            note: 音符信息
            duration: 持续时间（秒）
        """
        try:
            sound = self.generate_tone(note.frequency, duration)
            if sound:
                sound.play()
                # 等待播放完成
                if duration is None:
                    duration = self.duration
                time.sleep(duration)
        except Exception as e:
            print(f"播放音符失败: {e}")
    
    def play_frequency(self, frequency: float, duration: float = None):
        """
        播放指定频率的声音
        
        Args:
            frequency: 频率（Hz）
            duration: 持续时间（秒）
        """
        if not self.is_initialized:
            print("音频系统未初始化")
            return
        
        # 在新线程中播放音频
        thread = threading.Thread(
            target=self._play_frequency_thread,
            args=(frequency, duration),
            daemon=True
        )
        thread.start()
    
    def _play_frequency_thread(self, frequency: float, duration: float = None):
        """
        在线程中播放频率
        
        Args:
            frequency: 频率（Hz）
            duration: 持续时间（秒）
        """
        try:
            sound = self.generate_tone(frequency, duration)
            if sound:
                sound.play()
                # 等待播放完成
                if duration is None:
                    duration = self.duration
                time.sleep(duration)
        except Exception as e:
            print(f"播放频率失败: {e}")
    
    def stop_all(self):
        """停止所有正在播放的声音"""
        if self.is_initialized:
            pygame.mixer.stop()
    
    def set_volume(self, volume: float):
        """
        设置音量
        
        Args:
            volume: 音量（0.0-1.0）
        """
        if self.is_initialized:
            pygame.mixer.music.set_volume(max(0.0, min(1.0, volume)))
    
    def cleanup(self):
        """清理音频资源"""
        if self.is_initialized:
            pygame.mixer.quit()
            self.is_initialized = False
