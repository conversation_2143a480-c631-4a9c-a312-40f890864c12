#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五线谱视奏练习程序主应用类
"""

import tkinter as tk
from tkinter import ttk, Canvas, Frame, Button, Label
import random
from typing import Optional

from .note_model import NoteInfo, note_db
from .staff_renderer import <PERSON><PERSON>ender<PERSON>
from .piano_keyboard import PianoKeyboard
from .audio_player import AudioPlayer

class StaffPracticeApp:
    """五线谱视奏练习应用程序"""
    
    def __init__(self, root: tk.Tk):
        """
        初始化应用程序
        
        Args:
            root: Tkinter根窗口
        """
        self.root = root
        self.setup_window()
        
        # 当前模式：'sight_reading' 或 'piano_keyboard'
        self.current_mode = 'sight_reading'
        
        # 当前显示的音符
        self.current_note: Optional[NoteInfo] = None
        
        # 是否显示答案
        self.show_answer = False
        
        # 初始化组件
        self.audio_player = AudioPlayer(duration=1.5)
        
        self.create_widgets()
        self.setup_layout()
        self.bind_events()
        
        # 开始第一个练习
        self.next_note()
    
    def setup_window(self):
        """设置窗口属性"""
        self.root.title("五线谱视奏练习")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
    
    def create_widgets(self):
        """创建所有UI组件"""
        # 主框架
        self.main_frame = Frame(self.root)
        
        # 标题
        self.title_label = Label(
            self.main_frame, 
            text="五线谱视奏练习", 
            font=("Arial", 16, "bold")
        )
        
        # 模式选择框架
        self.mode_frame = Frame(self.main_frame)
        self.mode_var = tk.StringVar(value="sight_reading")
        
        self.sight_reading_radio = ttk.Radiobutton(
            self.mode_frame,
            text="视奏练习",
            variable=self.mode_var,
            value="sight_reading",
            command=self.on_mode_change
        )
        
        self.piano_keyboard_radio = ttk.Radiobutton(
            self.mode_frame,
            text="钢琴键盘",
            variable=self.mode_var,
            value="piano_keyboard",
            command=self.on_mode_change
        )
        
        # 五线谱画布
        self.staff_canvas = Canvas(
            self.main_frame,
            width=750,
            height=200,
            bg="white",
            relief="sunken",
            borderwidth=2
        )
        
        # 五线谱渲染器
        self.staff_renderer = StaffRenderer(self.staff_canvas, 750, 200)
        
        # 音名唱名显示框架
        self.note_info_frame = Frame(self.main_frame)
        
        # 音名显示
        self.note_name_label = Label(
            self.note_info_frame,
            text="音名: A B C D E F G",
            font=("Arial", 12)
        )
        
        # 唱名显示
        self.solfege_label = Label(
            self.note_info_frame,
            text="唱名: la si do re mi fa sol",
            font=("Arial", 12)
        )
        
        # 控制按钮框架
        self.control_frame = Frame(self.main_frame)
        
        self.next_button = Button(
            self.control_frame,
            text="下一题",
            command=self.next_note,
            font=("Arial", 10)
        )
        
        self.replay_button = Button(
            self.control_frame,
            text="重播",
            command=self.replay_note,
            font=("Arial", 10)
        )
        
        self.answer_button = Button(
            self.control_frame,
            text="显示答案",
            command=self.toggle_answer,
            font=("Arial", 10)
        )
        
        self.clear_button = Button(
            self.control_frame,
            text="清除五线谱",
            command=self.clear_staff,
            font=("Arial", 10)
        )
        
        # 钢琴键盘画布
        self.piano_canvas = Canvas(
            self.main_frame,
            width=750,
            height=120,
            bg="lightgray",
            relief="sunken",
            borderwidth=2
        )
        
        # 钢琴键盘
        self.piano_keyboard = PianoKeyboard(self.piano_canvas, 750, 120)
        self.piano_keyboard.set_key_click_callback(self.on_piano_key_click)
    
    def setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        self.title_label.pack(pady=(0, 10))
        
        # 模式选择
        self.mode_frame.pack(pady=(0, 10))
        self.sight_reading_radio.pack(side="left", padx=(0, 20))
        self.piano_keyboard_radio.pack(side="left")
        
        # 五线谱
        self.staff_canvas.pack(pady=(0, 10))
        
        # 音名唱名信息
        self.note_info_frame.pack(pady=(0, 10))
        self.note_name_label.pack()
        self.solfege_label.pack()
        
        # 控制按钮
        self.control_frame.pack(pady=(0, 10))
        self.next_button.pack(side="left", padx=(0, 10))
        self.replay_button.pack(side="left", padx=(0, 10))
        self.answer_button.pack(side="left", padx=(0, 10))
        self.clear_button.pack(side="left")
        
        # 钢琴键盘
        self.piano_canvas.pack(pady=(10, 0))
    
    def bind_events(self):
        """绑定事件"""
        self.root.bind("<KeyPress-space>", lambda e: self.next_note())
        self.root.bind("<KeyPress-r>", lambda e: self.replay_note())
        self.root.bind("<KeyPress-a>", lambda e: self.toggle_answer())
        self.root.bind("<KeyPress-c>", lambda e: self.clear_staff())
        
        # 窗口大小改变事件
        self.root.bind("<Configure>", self.on_window_resize)
    
    def on_mode_change(self):
        """模式切换处理"""
        self.current_mode = self.mode_var.get()
        
        if self.current_mode == "sight_reading":
            # 视奏练习模式
            self.next_button.config(state="normal")
            self.replay_button.config(state="normal")
            self.answer_button.config(state="normal")
            self.clear_button.config(state="disabled")
        else:
            # 钢琴键盘模式
            self.next_button.config(state="disabled")
            self.replay_button.config(state="disabled")
            self.answer_button.config(state="disabled")
            self.clear_button.config(state="normal")
            self.clear_staff()
    
    def next_note(self):
        """显示下一个随机音符"""
        if self.current_mode != "sight_reading":
            return
        
        # 随机选择一个白键音符
        self.current_note = note_db.get_random_white_key()
        
        # 清除五线谱并显示新音符
        self.staff_renderer.clear_notes()
        self.staff_renderer.add_note(self.current_note)
        
        # 播放音符
        self.audio_player.play_note(self.current_note)
        
        # 隐藏答案
        self.show_answer = False
        self.answer_button.config(text="显示答案")
        self.update_note_info_display()
    
    def replay_note(self):
        """重播当前音符"""
        if self.current_note and self.current_mode == "sight_reading":
            self.audio_player.play_note(self.current_note)
    
    def toggle_answer(self):
        """切换答案显示"""
        if self.current_mode != "sight_reading" or not self.current_note:
            return
        
        self.show_answer = not self.show_answer
        self.answer_button.config(text="隐藏答案" if self.show_answer else "显示答案")
        self.update_note_info_display()
    
    def clear_staff(self):
        """清除五线谱"""
        self.staff_renderer.clear_notes()
        self.current_note = None
        self.show_answer = False
        self.answer_button.config(text="显示答案")
        self.update_note_info_display()
    
    def on_piano_key_click(self, note: NoteInfo):
        """钢琴键点击处理"""
        if self.current_mode == "piano_keyboard":
            # 在五线谱上显示音符
            self.staff_renderer.add_note(note)
            
            # 播放音符
            self.audio_player.play_note(note, duration=0.8)
            
            # 更新当前音符信息
            self.current_note = note
            self.show_answer = True
            self.update_note_info_display()
    
    def update_note_info_display(self):
        """更新音名唱名显示"""
        if not self.current_note or not self.show_answer:
            # 显示所有音名和唱名
            self.note_name_label.config(text="音名: A B C D E F G")
            self.solfege_label.config(text="唱名: la si do re mi fa sol")
        else:
            # 高亮显示当前音符
            note_names = ["A", "B", "C", "D", "E", "F", "G"]
            solfeges = ["la", "si", "do", "re", "mi", "fa", "sol"]
            
            # 构建高亮显示的文本
            note_text = "音名: "
            solfege_text = "唱名: "
            
            for name in note_names:
                if name == self.current_note.note_name:
                    note_text += f"[{name}] "
                else:
                    note_text += f"{name} "
            
            for solfege in solfeges:
                if solfege == self.current_note.solfege:
                    solfege_text += f"[{solfege}] "
                else:
                    solfege_text += f"{solfege} "
            
            self.note_name_label.config(text=note_text.strip())
            self.solfege_label.config(text=solfege_text.strip())
    
    def on_window_resize(self, event):
        """窗口大小改变处理"""
        if event.widget == self.root:
            # 可以在这里调整画布大小
            pass
    
    def cleanup(self):
        """清理资源"""
        self.audio_player.cleanup()
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
