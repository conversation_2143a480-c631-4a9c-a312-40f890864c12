import sys, os
sys.path.append('src')
from src.note_model import note_db

# 测试关键音符
notes = ['C4', 'D4', 'E4', 'F4', 'G4', 'A4', 'B4', 'C5']
print("音符位置验证:")
for pitch in notes:
    note = note_db.get_note(pitch)
    print(f'{pitch}: staff_y={note.staff_y:2d}, pos={note.position}')

print("\n线上音符（应该是偶数staff_y）:")
line_notes = ['C4', 'E4', 'G4', 'B4']
for pitch in line_notes:
    note = note_db.get_note(pitch)
    print(f'{pitch}: staff_y={note.staff_y} (偶数={note.staff_y % 2 == 0})')

print("\n间中音符（应该是奇数staff_y）:")
space_notes = ['D4', 'F4', 'A4', 'C5']
for pitch in space_notes:
    note = note_db.get_note(pitch)
    print(f'{pitch}: staff_y={note.staff_y} (奇数={note.staff_y % 2 != 0})')
