# Markdown转PDF工具

一套完整的Markdown转PDF转换工具，支持中文字体、代码高亮、表格格式化等功能。

## 功能特性

- ✅ **中文字体支持** - 完美支持中文显示
- ✅ **代码高亮** - 支持多种编程语言语法高亮
- ✅ **表格格式化** - 美观的表格样式
- ✅ **图片嵌入** - 支持本地图片和网络图片
- ✅ **自定义样式** - 可自定义CSS样式
- ✅ **批量转换** - 支持批量转换多个文件
- ✅ **多种页面设置** - 支持不同页面大小和边距

## 安装依赖

### 1. 安装Python依赖
```bash
pip install -r requirements_pdf.txt
```

### 2. 安装wkhtmltopdf

**macOS:**
```bash
brew install wkhtmltopdf
```

**Ubuntu/Debian:**
```bash
sudo apt-get install wkhtmltopdf
```

**CentOS/RHEL:**
```bash
sudo yum install wkhtmltopdf
```

**Windows:**
从官网下载安装包：https://wkhtmltopdf.org/downloads.html

## 使用方法

### 单文件转换

```bash
# 基本用法
python md_to_pdf.py input.md

# 指定输出文件
python md_to_pdf.py input.md -o output.pdf

# 自定义页面设置
python md_to_pdf.py input.md --page-size A4 --margin 1in
```

### 批量转换

```bash
# 转换当前目录下所有MD文件
python batch_md_to_pdf.py

# 转换指定目录
python batch_md_to_pdf.py /path/to/markdown/files

# 递归转换子目录
python batch_md_to_pdf.py . -r

# 指定输出目录
python batch_md_to_pdf.py . -o ./pdf_output

# 排除特定文件
python batch_md_to_pdf.py . --exclude README.md CHANGELOG.md
```

## 命令行参数

### md_to_pdf.py 参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `input` | 输入的Markdown文件路径 | 必需 |
| `-o, --output` | 输出的PDF文件路径 | 自动生成 |
| `--page-size` | 页面大小 | A4 |
| `--margin` | 页边距 | 0.75in |

### batch_md_to_pdf.py 参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `directory` | 要转换的目录路径 | 当前目录 |
| `-r, --recursive` | 递归搜索子目录 | False |
| `-o, --output-dir` | 输出目录 | 与源文件同目录 |
| `--page-size` | 页面大小 | A4 |
| `--margin` | 页边距 | 0.75in |
| `--exclude` | 排除的文件名模式 | 无 |

## 支持的页面大小

- A4 (默认)
- A3
- A5
- Letter
- Legal
- Tabloid

## 样式自定义

工具内置了优美的CSS样式，支持：

- 中文字体优化
- 代码块语法高亮
- 表格美化
- 标题层级样式
- 引用块样式
- 链接样式
- 图片样式

如需自定义样式，可以修改 `md_to_pdf.py` 中的 `_get_default_css()` 方法。

## 使用示例

### 示例1：转换单个文件
```bash
python md_to_pdf.py README.md
```

### 示例2：批量转换当前目录
```bash
python batch_md_to_pdf.py
```

### 示例3：转换项目文档
```bash
python batch_md_to_pdf.py ./docs -r -o ./pdf_docs --exclude temp.md draft.md
```

## 故障排除

### 1. wkhtmltopdf未找到
```
❌ 转换失败: 未找到wkhtmltopdf，请先安装: brew install wkhtmltopdf
```
**解决方案：** 按照上述安装说明安装wkhtmltopdf

### 2. 中文字体显示问题
如果PDF中中文显示异常，请确保系统已安装中文字体。

### 3. 图片无法显示
确保图片路径正确，支持相对路径和绝对路径。

### 4. 内存不足
转换大文件时可能出现内存不足，可以尝试：
- 分割大文件
- 增加系统内存
- 优化图片大小

## 文件结构

```
.
├── md_to_pdf.py              # 主转换工具
├── batch_md_to_pdf.py        # 批量转换工具
├── requirements_pdf.txt      # Python依赖
└── README_PDF_CONVERTER.md   # 使用说明
```

## 技术实现

- **Markdown解析：** 使用 `markdown` 库
- **PDF生成：** 使用 `pdfkit` + `wkhtmltopdf`
- **代码高亮：** 使用 `Pygments`
- **样式设计：** 自定义CSS样式

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
