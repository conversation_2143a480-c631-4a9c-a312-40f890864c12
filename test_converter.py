#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MD转PDF工具测试脚本
"""

import os
import sys
import tempfile
from pathlib import Path

def create_test_markdown():
    """创建测试用的Markdown文件"""
    test_content = """# 测试文档

## 简介
这是一个测试Markdown转PDF功能的文档。

## 功能测试

### 1. 中文支持测试
这里是中文内容，包含各种标点符号：，。！？；：""''（）【】

### 2. 代码块测试

```python
def hello_world():
    print("Hello, World!")
    return "测试中文注释"

# 这是注释
for i in range(5):
    print(f"数字: {i}")
```

```javascript
function greet(name) {
    console.log(`你好, ${name}!`);
}

greet("世界");
```

### 3. 表格测试

| 功能 | 状态 | 说明 |
|------|------|------|
| 中文支持 | ✅ | 完全支持 |
| 代码高亮 | ✅ | 多语言支持 |
| 表格格式 | ✅ | 美观排版 |
| 图片嵌入 | ✅ | 支持本地图片 |

### 4. 列表测试

**有序列表：**
1. 第一项
2. 第二项
   - 子项目A
   - 子项目B
3. 第三项

**无序列表：**
- 项目一
- 项目二
  - 嵌套项目
  - 另一个嵌套项目
- 项目三

### 5. 引用测试

> 这是一个引用块的例子。
> 
> 可以包含多行内容，支持中文和英文混合。
> 
> — 测试作者

### 6. 链接测试

这里有一个[链接示例](https://www.example.com)。

### 7. 强调测试

**粗体文本** 和 *斜体文本* 以及 ***粗斜体文本***。

还有 `行内代码` 的测试。

### 8. 分割线测试

---

## 结论

如果您能看到格式良好的PDF文档，说明转换工具工作正常！

---

*测试完成时间：2025年1月*
"""
    
    # 创建测试文件
    test_file = "test_document.md"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    return test_file


def test_single_conversion():
    """测试单文件转换"""
    print("🧪 测试单文件转换...")
    
    try:
        # 创建测试文件
        test_file = create_test_markdown()
        
        # 导入转换器
        from md_to_pdf import MarkdownToPDFConverter
        
        # 执行转换
        converter = MarkdownToPDFConverter()
        output_file = converter.convert(test_file)
        
        # 检查结果
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"✅ 单文件转换成功!")
            print(f"   输入文件: {test_file}")
            print(f"   输出文件: {output_file}")
            print(f"   文件大小: {file_size} 字节")
            return True
        else:
            print("❌ 输出文件未生成")
            return False
            
    except Exception as e:
        print(f"❌ 单文件转换失败: {str(e)}")
        return False


def test_batch_conversion():
    """测试批量转换"""
    print("\n🧪 测试批量转换...")
    
    try:
        # 创建多个测试文件
        test_files = []
        for i in range(3):
            content = f"""# 测试文档 {i+1}

这是第 {i+1} 个测试文档。

## 内容

- 项目 {i+1}.1
- 项目 {i+1}.2
- 项目 {i+1}.3

```python
print(f"这是第 {i+1} 个文档")
```

完成时间：2025年1月
"""
            filename = f"test_doc_{i+1}.md"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            test_files.append(filename)
        
        # 执行批量转换
        import subprocess
        result = subprocess.run([
            sys.executable, 'batch_md_to_pdf.py', '.',
            '--exclude', 'README.md', 'README_PDF_CONVERTER.md'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 批量转换成功!")
            print("   转换输出:")
            print(result.stdout)
            
            # 检查生成的PDF文件
            pdf_count = 0
            for test_file in test_files:
                pdf_file = test_file.replace('.md', '.pdf')
                if os.path.exists(pdf_file):
                    pdf_count += 1
            
            print(f"   生成PDF文件: {pdf_count}/{len(test_files)}")
            return pdf_count == len(test_files)
        else:
            print("❌ 批量转换失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 批量转换测试失败: {str(e)}")
        return False


def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    missing_deps = []
    
    try:
        import markdown
        print("✅ markdown 已安装")
    except ImportError:
        missing_deps.append("markdown")
        print("❌ markdown 未安装")
    
    try:
        import pdfkit
        print("✅ pdfkit 已安装")
    except ImportError:
        missing_deps.append("pdfkit")
        print("❌ pdfkit 未安装")
    
    # 检查wkhtmltopdf
    import shutil
    if shutil.which('wkhtmltopdf'):
        print("✅ wkhtmltopdf 已安装")
    else:
        missing_deps.append("wkhtmltopdf")
        print("❌ wkhtmltopdf 未安装")
    
    if missing_deps:
        print(f"\n❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        if 'wkhtmltopdf' in missing_deps:
            print("  brew install wkhtmltopdf  # macOS")
        if any(dep in missing_deps for dep in ['markdown', 'pdfkit']):
            print("  pip install -r requirements_pdf.txt")
        return False
    
    print("✅ 所有依赖已安装")
    return True


def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_patterns = [
        "test_document.*",
        "test_doc_*.*"
    ]
    
    import glob
    cleaned_count = 0
    
    for pattern in test_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                cleaned_count += 1
                print(f"   删除: {file}")
            except Exception as e:
                print(f"   删除失败: {file} - {str(e)}")
    
    print(f"✅ 清理完成，删除了 {cleaned_count} 个文件")


def main():
    """主测试函数"""
    print("🚀 MD转PDF工具测试开始\n")
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装必要的依赖")
        return False
    
    # 测试单文件转换
    single_test = test_single_conversion()
    
    # 测试批量转换
    batch_test = test_batch_conversion()
    
    # 输出测试结果
    print(f"\n📊 测试结果:")
    print(f"   单文件转换: {'✅ 通过' if single_test else '❌ 失败'}")
    print(f"   批量转换: {'✅ 通过' if batch_test else '❌ 失败'}")
    
    # 清理测试文件
    cleanup_test_files()
    
    if single_test and batch_test:
        print(f"\n🎉 所有测试通过! MD转PDF工具工作正常。")
        return True
    else:
        print(f"\n❌ 部分测试失败，请检查错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
